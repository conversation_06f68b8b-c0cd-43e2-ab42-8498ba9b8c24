/**
 * QR-Code Based Student Attendance and Monitoring System
 * Tanauan School of Arts and Trades
 * Complete Node.js Backend Server
 */

const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const QRCode = require('qrcode');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const compression = require('compression');
const winston = require('winston');
require('dotenv').config();

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3001;

// Configure Winston logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'qrsams-backend' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

// Create logs directory if it doesn't exist
if (!fs.existsSync('logs')) {
  fs.mkdirSync('logs');
}

// Create uploads directory if it doesn't exist
if (!fs.existsSync('uploads')) {
  fs.mkdirSync('uploads');
}
if (!fs.existsSync('uploads/photos')) {
  fs.mkdirSync('uploads/photos');
}
if (!fs.existsSync('uploads/qr-codes')) {
  fs.mkdirSync('uploads/qr-codes');
}

// Database configuration
const DB_PATH = process.env.DB_PATH || './database/qrsams.db';

// Create database directory if it doesn't exist
if (!fs.existsSync('database')) {
  fs.mkdirSync('database');
}

// Initialize SQLite database
const db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    logger.error('Error opening database:', err);
  } else {
    logger.info('Connected to SQLite database');
    initializeDatabase();
  }
});

// Middleware configuration
app.use(helmet()); // Security headers
app.use(compression()); // Gzip compression
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static file serving
app.use('/uploads', express.static('uploads'));

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/photos/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'student-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    req.user = user;
    next();
  });
};

// Request logging middleware
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path} - ${req.ip}`);
  next();
});

// Utility functions
function generateStudentNumber() {
  const year = new Date().getFullYear();
  const randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `${year}-${randomNum}`;
}

function generateSecureQRCode(studentData) {
  const timestamp = Date.now();
  const dataString = `${studentData.student_number}-${studentData.first_name}-${studentData.last_name}-${timestamp}`;
  return crypto.createHash('sha256').update(dataString).digest('hex').substring(0, 16);
}

function validatePhilippinePhone(phone) {
  // Philippine mobile number format: +63XXXXXXXXXX or 09XXXXXXXXX
  const phoneRegex = /^(\+63|0)9\d{9}$/;
  return phoneRegex.test(phone);
}

// Database initialization function
function initializeDatabase() {
  const tables = [
    // Students table
    `CREATE TABLE IF NOT EXISTS students (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      student_number TEXT UNIQUE NOT NULL,
      first_name TEXT NOT NULL,
      last_name TEXT NOT NULL,
      middle_name TEXT,
      grade_level TEXT NOT NULL CHECK (grade_level IN ('Grade 11', 'Grade 12')),
      section TEXT NOT NULL,
      photo_url TEXT,
      qr_code TEXT UNIQUE NOT NULL,
      date_enrolled DATE NOT NULL DEFAULT CURRENT_DATE,
      status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'transferred', 'graduated')),
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // Teachers table
    `CREATE TABLE IF NOT EXISTS teachers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      teacher_id TEXT UNIQUE NOT NULL,
      first_name TEXT NOT NULL,
      last_name TEXT NOT NULL,
      username TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      subjects TEXT, -- JSON array of subjects
      role TEXT DEFAULT 'teacher' CHECK (role IN ('teacher', 'admin', 'principal')),
      status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // Classes table
    `CREATE TABLE IF NOT EXISTS classes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      subject_name TEXT NOT NULL,
      teacher_id INTEGER NOT NULL,
      grade_level TEXT NOT NULL CHECK (grade_level IN ('Grade 11', 'Grade 12')),
      section TEXT NOT NULL,
      schedule TEXT NOT NULL, -- JSON object with days and times
      room TEXT NOT NULL,
      academic_year TEXT NOT NULL,
      status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (teacher_id) REFERENCES teachers (id)
    )`,
    
    // Attendance table
    `CREATE TABLE IF NOT EXISTS attendance (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      student_id INTEGER NOT NULL,
      class_id INTEGER NOT NULL,
      date DATE NOT NULL,
      time_in TIME NOT NULL,
      status TEXT NOT NULL CHECK (status IN ('present', 'late', 'absent', 'excused')),
      marked_by INTEGER NOT NULL,
      notes TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (student_id) REFERENCES students (id),
      FOREIGN KEY (class_id) REFERENCES classes (id),
      FOREIGN KEY (marked_by) REFERENCES teachers (id),
      UNIQUE(student_id, class_id, date)
    )`,
    
    // Parents table
    `CREATE TABLE IF NOT EXISTS parents (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      student_id INTEGER NOT NULL,
      first_name TEXT NOT NULL,
      last_name TEXT NOT NULL,
      relationship TEXT NOT NULL CHECK (relationship IN ('father', 'mother', 'guardian', 'grandmother', 'grandfather')),
      phone_number TEXT NOT NULL,
      email TEXT,
      address TEXT,
      is_primary BOOLEAN DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (student_id) REFERENCES students (id)
    )`,
    
    // SMS logs table
    `CREATE TABLE IF NOT EXISTS sms_logs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      student_id INTEGER NOT NULL,
      parent_phone TEXT NOT NULL,
      message TEXT NOT NULL,
      status TEXT NOT NULL CHECK (status IN ('pending', 'sent', 'failed', 'delivered')),
      sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      delivered_at DATETIME,
      error_message TEXT,
      FOREIGN KEY (student_id) REFERENCES students (id)
    )`
  ];

  // Create tables
  tables.forEach((tableSQL, index) => {
    db.run(tableSQL, (err) => {
      if (err) {
        logger.error(`Error creating table ${index + 1}:`, err);
      } else {
        logger.info(`Table ${index + 1} created or already exists`);
      }
    });
  });

  // Insert sample data after tables are created
  setTimeout(() => {
    insertSampleData();
  }, 1000);
}

// Insert sample data function
async function insertSampleData() {
  try {
    // Check if admin teacher exists
    db.get("SELECT id FROM teachers WHERE username = 'admin'", async (err, row) => {
      if (err) {
        logger.error('Error checking admin user:', err);
        return;
      }

      if (!row) {
        // Create default admin teacher
        const hashedPassword = await bcrypt.hash('admin123', 10);
        const adminData = {
          teacher_id: 'TSAT-2024-001',
          first_name: 'Maria',
          last_name: 'Santos',
          username: 'admin',
          password_hash: hashedPassword,
          subjects: JSON.stringify(['Mathematics', 'Science', 'English']),
          role: 'admin'
        };

        db.run(`INSERT INTO teachers (teacher_id, first_name, last_name, username, password_hash, subjects, role)
                VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [adminData.teacher_id, adminData.first_name, adminData.last_name,
           adminData.username, adminData.password_hash, adminData.subjects, adminData.role],
          function(err) {
            if (err) {
              logger.error('Error creating admin user:', err);
            } else {
              logger.info('Admin user created successfully');
            }
          }
        );
      }
    });

    // Insert sample teachers
    const sampleTeachers = [
      {
        teacher_id: 'TSAT-2024-002',
        first_name: 'Juan',
        last_name: 'Dela Cruz',
        username: 'jdelacruz',
        subjects: JSON.stringify(['Filipino', 'Araling Panlipunan']),
        role: 'teacher'
      },
      {
        teacher_id: 'TSAT-2024-003',
        first_name: 'Ana',
        last_name: 'Reyes',
        username: 'areyes',
        subjects: JSON.stringify(['English', 'Research']),
        role: 'teacher'
      }
    ];

    for (const teacher of sampleTeachers) {
      const hashedPassword = await bcrypt.hash('teacher123', 10);
      db.run(`INSERT OR IGNORE INTO teachers (teacher_id, first_name, last_name, username, password_hash, subjects, role)
              VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [teacher.teacher_id, teacher.first_name, teacher.last_name,
         teacher.username, hashedPassword, teacher.subjects, teacher.role]
      );
    }

    // Insert sample students with Filipino names
    const sampleStudents = [
      {
        first_name: 'Jose',
        last_name: 'Rizal',
        middle_name: 'Protacio',
        grade_level: 'Grade 11',
        section: 'STEM-A'
      },
      {
        first_name: 'Maria',
        last_name: 'Clara',
        middle_name: 'Santos',
        grade_level: 'Grade 11',
        section: 'STEM-A'
      },
      {
        first_name: 'Andres',
        last_name: 'Bonifacio',
        middle_name: 'Deodato',
        grade_level: 'Grade 12',
        section: 'ABM-A'
      },
      {
        first_name: 'Gabriela',
        last_name: 'Silang',
        middle_name: 'Cariño',
        grade_level: 'Grade 12',
        section: 'HUMSS-A'
      }
    ];

    for (const student of sampleStudents) {
      const studentNumber = generateStudentNumber();
      const qrCode = generateSecureQRCode({ ...student, student_number: studentNumber });

      db.run(`INSERT OR IGNORE INTO students (student_number, first_name, last_name, middle_name, grade_level, section, qr_code)
              VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [studentNumber, student.first_name, student.last_name, student.middle_name,
         student.grade_level, student.section, qrCode]
      );
    }

    // Insert sample classes
    const sampleClasses = [
      {
        subject_name: 'General Mathematics',
        teacher_id: 1,
        grade_level: 'Grade 11',
        section: 'STEM-A',
        schedule: JSON.stringify({
          monday: '08:00-09:30',
          wednesday: '08:00-09:30',
          friday: '08:00-09:30'
        }),
        room: 'Room 101',
        academic_year: '2024-2025'
      },
      {
        subject_name: 'Earth and Life Science',
        teacher_id: 1,
        grade_level: 'Grade 11',
        section: 'STEM-A',
        schedule: JSON.stringify({
          tuesday: '10:00-11:30',
          thursday: '10:00-11:30'
        }),
        room: 'Science Lab',
        academic_year: '2024-2025'
      }
    ];

    for (const classData of sampleClasses) {
      db.run(`INSERT OR IGNORE INTO classes (subject_name, teacher_id, grade_level, section, schedule, room, academic_year)
              VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [classData.subject_name, classData.teacher_id, classData.grade_level,
         classData.section, classData.schedule, classData.room, classData.academic_year]
      );
    }

    logger.info('Sample data insertion completed');
  } catch (error) {
    logger.error('Error inserting sample data:', error);
  }
}

// =============================================================================
// AUTHENTICATION ROUTES
// =============================================================================

// POST /api/auth/login - Teacher authentication
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password are required' });
    }

    // Find teacher by username
    db.get("SELECT * FROM teachers WHERE username = ? AND status = 'active'", [username], async (err, teacher) => {
      if (err) {
        logger.error('Database error during login:', err);
        return res.status(500).json({ error: 'Internal server error' });
      }

      if (!teacher) {
        return res.status(401).json({ error: 'Invalid credentials' });
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, teacher.password_hash);
      if (!isValidPassword) {
        return res.status(401).json({ error: 'Invalid credentials' });
      }

      // Generate JWT token
      const token = jwt.sign(
        {
          id: teacher.id,
          username: teacher.username,
          role: teacher.role,
          teacher_id: teacher.teacher_id
        },
        JWT_SECRET,
        { expiresIn: '8h' }
      );

      // Remove password hash from response
      const { password_hash, ...teacherData } = teacher;

      res.json({
        message: 'Login successful',
        token,
        teacher: {
          ...teacherData,
          subjects: JSON.parse(teacher.subjects || '[]')
        }
      });

      logger.info(`Teacher ${username} logged in successfully`);
    });
  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/auth/logout
app.post('/api/auth/logout', authenticateToken, (req, res) => {
  // In a production environment, you might want to blacklist the token
  res.json({ message: 'Logout successful' });
  logger.info(`Teacher ${req.user.username} logged out`);
});

// GET /api/auth/verify - Verify token validity
app.get('/api/auth/verify', authenticateToken, (req, res) => {
  res.json({
    valid: true,
    user: {
      id: req.user.id,
      username: req.user.username,
      role: req.user.role,
      teacher_id: req.user.teacher_id
    }
  });
});

// =============================================================================
// STUDENT MANAGEMENT ROUTES
// =============================================================================

// GET /api/students - Get all students with pagination
app.get('/api/students', authenticateToken, (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    const search = req.query.search || '';
    const gradeLevel = req.query.grade_level || '';
    const section = req.query.section || '';

    let whereClause = "WHERE 1=1";
    let params = [];

    if (search) {
      whereClause += " AND (first_name LIKE ? OR last_name LIKE ? OR student_number LIKE ?)";
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    if (gradeLevel) {
      whereClause += " AND grade_level = ?";
      params.push(gradeLevel);
    }

    if (section) {
      whereClause += " AND section = ?";
      params.push(section);
    }

    // Get total count
    db.get(`SELECT COUNT(*) as total FROM students ${whereClause}`, params, (err, countResult) => {
      if (err) {
        logger.error('Error counting students:', err);
        return res.status(500).json({ error: 'Internal server error' });
      }

      // Get students with pagination
      const studentsQuery = `
        SELECT id, student_number, first_name, last_name, middle_name,
               grade_level, section, photo_url, date_enrolled, status, created_at
        FROM students
        ${whereClause}
        ORDER BY last_name, first_name
        LIMIT ? OFFSET ?
      `;

      db.all(studentsQuery, [...params, limit, offset], (err, students) => {
        if (err) {
          logger.error('Error fetching students:', err);
          return res.status(500).json({ error: 'Internal server error' });
        }

        res.json({
          students,
          pagination: {
            page,
            limit,
            total: countResult.total,
            totalPages: Math.ceil(countResult.total / limit)
          }
        });
      });
    });
  } catch (error) {
    logger.error('Error in GET /api/students:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/students - Add new student
app.post('/api/students', authenticateToken, upload.single('photo'), async (req, res) => {
  try {
    const { first_name, last_name, middle_name, grade_level, section } = req.body;

    // Validation
    if (!first_name || !last_name || !grade_level || !section) {
      return res.status(400).json({
        error: 'First name, last name, grade level, and section are required'
      });
    }

    if (!['Grade 11', 'Grade 12'].includes(grade_level)) {
      return res.status(400).json({
        error: 'Grade level must be either "Grade 11" or "Grade 12"'
      });
    }

    // Generate student number and QR code
    const studentNumber = generateStudentNumber();
    const studentData = { first_name, last_name, middle_name, grade_level, section, student_number: studentNumber };
    const qrCode = generateSecureQRCode(studentData);

    // Handle photo upload
    let photoUrl = null;
    if (req.file) {
      photoUrl = `/uploads/photos/${req.file.filename}`;
    }

    // Insert student into database
    db.run(`INSERT INTO students (student_number, first_name, last_name, middle_name, grade_level, section, photo_url, qr_code)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [studentNumber, first_name, last_name, middle_name, grade_level, section, photoUrl, qrCode],
      function(err) {
        if (err) {
          logger.error('Error creating student:', err);
          if (err.code === 'SQLITE_CONSTRAINT') {
            return res.status(409).json({ error: 'Student number already exists' });
          }
          return res.status(500).json({ error: 'Internal server error' });
        }

        // Generate QR code image
        const qrCodeData = JSON.stringify({
          student_id: this.lastID,
          student_number: studentNumber,
          qr_code: qrCode,
          timestamp: Date.now()
        });

        QRCode.toFile(`uploads/qr-codes/${studentNumber}.png`, qrCodeData, (qrErr) => {
          if (qrErr) {
            logger.error('Error generating QR code image:', qrErr);
          }
        });

        res.status(201).json({
          message: 'Student created successfully',
          student: {
            id: this.lastID,
            student_number: studentNumber,
            first_name,
            last_name,
            middle_name,
            grade_level,
            section,
            photo_url: photoUrl,
            qr_code: qrCode
          }
        });

        logger.info(`Student ${studentNumber} created successfully`);
      }
    );
  } catch (error) {
    logger.error('Error in POST /api/students:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/students/:id - Get student profile
app.get('/api/students/:id', authenticateToken, (req, res) => {
  try {
    const studentId = req.params.id;

    const query = `
      SELECT s.*,
             GROUP_CONCAT(
               json_object(
                 'id', p.id,
                 'first_name', p.first_name,
                 'last_name', p.last_name,
                 'relationship', p.relationship,
                 'phone_number', p.phone_number,
                 'email', p.email,
                 'address', p.address,
                 'is_primary', p.is_primary
               )
             ) as parents
      FROM students s
      LEFT JOIN parents p ON s.id = p.student_id
      WHERE s.id = ?
      GROUP BY s.id
    `;

    db.get(query, [studentId], (err, student) => {
      if (err) {
        logger.error('Error fetching student profile:', err);
        return res.status(500).json({ error: 'Internal server error' });
      }

      if (!student) {
        return res.status(404).json({ error: 'Student not found' });
      }

      // Parse parents data
      let parents = [];
      if (student.parents) {
        try {
          parents = student.parents.split(',').map(p => JSON.parse(p));
        } catch (parseErr) {
          logger.error('Error parsing parents data:', parseErr);
        }
      }

      const { parents: _, ...studentData } = student;

      res.json({
        student: {
          ...studentData,
          parents
        }
      });
    });
  } catch (error) {
    logger.error('Error in GET /api/students/:id:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// =============================================================================
// ATTENDANCE MANAGEMENT ROUTES
// =============================================================================

// POST /api/attendance/mark - Mark attendance via QR scan
app.post('/api/attendance/mark', authenticateToken, (req, res) => {
  try {
    const { qr_code, class_id, status = 'present', notes = '' } = req.body;

    if (!qr_code || !class_id) {
      return res.status(400).json({ error: 'QR code and class ID are required' });
    }

    // Find student by QR code
    db.get("SELECT * FROM students WHERE qr_code = ? AND status = 'active'", [qr_code], (err, student) => {
      if (err) {
        logger.error('Error finding student by QR code:', err);
        return res.status(500).json({ error: 'Internal server error' });
      }

      if (!student) {
        return res.status(404).json({ error: 'Invalid QR code or student not found' });
      }

      // Check if class exists
      db.get("SELECT * FROM classes WHERE id = ? AND status = 'active'", [class_id], (classErr, classData) => {
        if (classErr) {
          logger.error('Error finding class:', classErr);
          return res.status(500).json({ error: 'Internal server error' });
        }

        if (!classData) {
          return res.status(404).json({ error: 'Class not found' });
        }

        const today = new Date().toISOString().split('T')[0];
        const currentTime = new Date().toTimeString().split(' ')[0];

        // Check if attendance already marked for today
        db.get(`SELECT * FROM attendance WHERE student_id = ? AND class_id = ? AND date = ?`,
          [student.id, class_id, today], (attendanceErr, existingAttendance) => {
            if (attendanceErr) {
              logger.error('Error checking existing attendance:', attendanceErr);
              return res.status(500).json({ error: 'Internal server error' });
            }

            if (existingAttendance) {
              return res.status(409).json({
                error: 'Attendance already marked for today',
                attendance: existingAttendance
              });
            }

            // Mark attendance
            db.run(`INSERT INTO attendance (student_id, class_id, date, time_in, status, marked_by, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)`,
              [student.id, class_id, today, currentTime, status, req.user.id, notes],
              function(insertErr) {
                if (insertErr) {
                  logger.error('Error marking attendance:', insertErr);
                  return res.status(500).json({ error: 'Internal server error' });
                }

                res.status(201).json({
                  message: 'Attendance marked successfully',
                  attendance: {
                    id: this.lastID,
                    student: {
                      id: student.id,
                      student_number: student.student_number,
                      first_name: student.first_name,
                      last_name: student.last_name,
                      grade_level: student.grade_level,
                      section: student.section
                    },
                    class_id,
                    date: today,
                    time_in: currentTime,
                    status,
                    notes
                  }
                });

                logger.info(`Attendance marked for student ${student.student_number} in class ${class_id}`);
              }
            );
          }
        );
      });
    });
  } catch (error) {
    logger.error('Error in POST /api/attendance/mark:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/attendance/class/:classId - Get class attendance
app.get('/api/attendance/class/:classId', authenticateToken, (req, res) => {
  try {
    const { classId } = req.params;
    const date = req.query.date || new Date().toISOString().split('T')[0];
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const offset = (page - 1) * limit;

    // Get class information
    db.get("SELECT * FROM classes WHERE id = ?", [classId], (err, classData) => {
      if (err) {
        logger.error('Error fetching class:', err);
        return res.status(500).json({ error: 'Internal server error' });
      }

      if (!classData) {
        return res.status(404).json({ error: 'Class not found' });
      }

      // Get attendance records for the class and date
      const attendanceQuery = `
        SELECT a.*, s.student_number, s.first_name, s.last_name, s.middle_name,
               s.grade_level, s.section, s.photo_url,
               t.first_name as teacher_first_name, t.last_name as teacher_last_name
        FROM attendance a
        JOIN students s ON a.student_id = s.id
        JOIN teachers t ON a.marked_by = t.id
        WHERE a.class_id = ? AND a.date = ?
        ORDER BY a.time_in ASC
        LIMIT ? OFFSET ?
      `;

      db.all(attendanceQuery, [classId, date, limit, offset], (attendanceErr, attendanceRecords) => {
        if (attendanceErr) {
          logger.error('Error fetching attendance records:', attendanceErr);
          return res.status(500).json({ error: 'Internal server error' });
        }

        // Get total count for pagination
        db.get(`SELECT COUNT(*) as total FROM attendance WHERE class_id = ? AND date = ?`,
          [classId, date], (countErr, countResult) => {
            if (countErr) {
              logger.error('Error counting attendance records:', countErr);
              return res.status(500).json({ error: 'Internal server error' });
            }

            res.json({
              class: {
                ...classData,
                schedule: JSON.parse(classData.schedule || '{}')
              },
              date,
              attendance: attendanceRecords,
              pagination: {
                page,
                limit,
                total: countResult.total,
                totalPages: Math.ceil(countResult.total / limit)
              }
            });
          }
        );
      });
    });
  } catch (error) {
    logger.error('Error in GET /api/attendance/class/:classId:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// =============================================================================
// SMS NOTIFICATION ROUTES
// =============================================================================

// POST /api/sms/send - Send SMS notification
app.post('/api/sms/send', authenticateToken, (req, res) => {
  try {
    const { student_id, message, parent_phone } = req.body;

    if (!student_id || !message) {
      return res.status(400).json({ error: 'Student ID and message are required' });
    }

    // Get student and parent information
    db.get(`
      SELECT s.*, p.phone_number, p.first_name as parent_first_name, p.last_name as parent_last_name
      FROM students s
      LEFT JOIN parents p ON s.id = p.student_id AND p.is_primary = 1
      WHERE s.id = ?
    `, [student_id], (err, studentData) => {
      if (err) {
        logger.error('Error fetching student for SMS:', err);
        return res.status(500).json({ error: 'Internal server error' });
      }

      if (!studentData) {
        return res.status(404).json({ error: 'Student not found' });
      }

      const phoneNumber = parent_phone || studentData.phone_number;

      if (!phoneNumber) {
        return res.status(400).json({ error: 'No parent phone number available' });
      }

      if (!validatePhilippinePhone(phoneNumber)) {
        return res.status(400).json({ error: 'Invalid Philippine phone number format' });
      }

      // In a real implementation, you would integrate with an SMS service like Twilio
      // For now, we'll just log the SMS and mark it as sent
      db.run(`INSERT INTO sms_logs (student_id, parent_phone, message, status)
              VALUES (?, ?, ?, ?)`,
        [student_id, phoneNumber, message, 'sent'],
        function(smsErr) {
          if (smsErr) {
            logger.error('Error logging SMS:', smsErr);
            return res.status(500).json({ error: 'Internal server error' });
          }

          res.json({
            message: 'SMS notification sent successfully',
            sms_log: {
              id: this.lastID,
              student_id,
              parent_phone: phoneNumber,
              message,
              status: 'sent',
              sent_at: new Date().toISOString()
            }
          });

          logger.info(`SMS sent to ${phoneNumber} for student ${studentData.student_number}`);
        }
      );
    });
  } catch (error) {
    logger.error('Error in POST /api/sms/send:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// =============================================================================
// REPORTING ROUTES
// =============================================================================

// GET /api/reports/sf2 - Generate SF2 form data
app.get('/api/reports/sf2', authenticateToken, (req, res) => {
  try {
    const { class_id, month, year } = req.query;

    if (!class_id || !month || !year) {
      return res.status(400).json({
        error: 'Class ID, month, and year are required'
      });
    }

    const startDate = `${year}-${month.padStart(2, '0')}-01`;
    const endDate = `${year}-${month.padStart(2, '0')}-31`;

    // Get class information
    db.get(`
      SELECT c.*, t.first_name as teacher_first_name, t.last_name as teacher_last_name
      FROM classes c
      JOIN teachers t ON c.teacher_id = t.id
      WHERE c.id = ?
    `, [class_id], (err, classData) => {
      if (err) {
        logger.error('Error fetching class for SF2:', err);
        return res.status(500).json({ error: 'Internal server error' });
      }

      if (!classData) {
        return res.status(404).json({ error: 'Class not found' });
      }

      // Get students enrolled in the class (by grade level and section)
      db.all(`
        SELECT s.*,
               COUNT(CASE WHEN a.status = 'present' THEN 1 END) as present_days,
               COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_days,
               COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_days,
               COUNT(CASE WHEN a.status = 'excused' THEN 1 END) as excused_days
        FROM students s
        LEFT JOIN attendance a ON s.id = a.student_id
          AND a.class_id = ?
          AND a.date BETWEEN ? AND ?
        WHERE s.grade_level = ? AND s.section = ? AND s.status = 'active'
        GROUP BY s.id
        ORDER BY s.last_name, s.first_name
      `, [class_id, startDate, endDate, classData.grade_level, classData.section], (studentsErr, students) => {
        if (studentsErr) {
          logger.error('Error fetching students for SF2:', studentsErr);
          return res.status(500).json({ error: 'Internal server error' });
        }

        res.json({
          report_type: 'SF2',
          class: {
            ...classData,
            schedule: JSON.parse(classData.schedule || '{}'),
            teacher_name: `${classData.teacher_first_name} ${classData.teacher_last_name}`
          },
          period: {
            month: parseInt(month),
            year: parseInt(year),
            start_date: startDate,
            end_date: endDate
          },
          students: students.map(student => ({
            ...student,
            attendance_summary: {
              present: student.present_days || 0,
              late: student.late_days || 0,
              absent: student.absent_days || 0,
              excused: student.excused_days || 0,
              total_days: (student.present_days || 0) + (student.late_days || 0) +
                         (student.absent_days || 0) + (student.excused_days || 0)
            }
          })),
          generated_at: new Date().toISOString(),
          generated_by: req.user.username
        });
      });
    });
  } catch (error) {
    logger.error('Error in GET /api/reports/sf2:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/reports/sf4 - Generate SF4 form data
app.get('/api/reports/sf4', authenticateToken, (req, res) => {
  try {
    const { grade_level, section, month, year } = req.query;

    if (!grade_level || !section || !month || !year) {
      return res.status(400).json({
        error: 'Grade level, section, month, and year are required'
      });
    }

    const startDate = `${year}-${month.padStart(2, '0')}-01`;
    const endDate = `${year}-${month.padStart(2, '0')}-31`;

    // Get all students in the grade level and section
    db.all(`
      SELECT s.*,
             COUNT(CASE WHEN a.status = 'present' THEN 1 END) as present_days,
             COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_days,
             COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_days,
             COUNT(CASE WHEN a.status = 'excused' THEN 1 END) as excused_days
      FROM students s
      LEFT JOIN attendance a ON s.id = a.student_id
        AND a.date BETWEEN ? AND ?
      WHERE s.grade_level = ? AND s.section = ? AND s.status = 'active'
      GROUP BY s.id
      ORDER BY s.last_name, s.first_name
    `, [startDate, endDate, grade_level, section], (err, students) => {
      if (err) {
        logger.error('Error fetching students for SF4:', err);
        return res.status(500).json({ error: 'Internal server error' });
      }

      // Get classes for this grade level and section
      db.all(`
        SELECT c.*, t.first_name as teacher_first_name, t.last_name as teacher_last_name
        FROM classes c
        JOIN teachers t ON c.teacher_id = t.id
        WHERE c.grade_level = ? AND c.section = ? AND c.status = 'active'
      `, [grade_level, section], (classErr, classes) => {
        if (classErr) {
          logger.error('Error fetching classes for SF4:', classErr);
          return res.status(500).json({ error: 'Internal server error' });
        }

        res.json({
          report_type: 'SF4',
          section_info: {
            grade_level,
            section,
            academic_year: '2024-2025'
          },
          period: {
            month: parseInt(month),
            year: parseInt(year),
            start_date: startDate,
            end_date: endDate
          },
          classes: classes.map(cls => ({
            ...cls,
            schedule: JSON.parse(cls.schedule || '{}'),
            teacher_name: `${cls.teacher_first_name} ${cls.teacher_last_name}`
          })),
          students: students.map(student => ({
            ...student,
            attendance_summary: {
              present: student.present_days || 0,
              late: student.late_days || 0,
              absent: student.absent_days || 0,
              excused: student.excused_days || 0,
              total_days: (student.present_days || 0) + (student.late_days || 0) +
                         (student.absent_days || 0) + (student.excused_days || 0),
              attendance_rate: ((student.present_days || 0) + (student.late_days || 0)) /
                              Math.max(1, (student.present_days || 0) + (student.late_days || 0) +
                                         (student.absent_days || 0) + (student.excused_days || 0)) * 100
            }
          })),
          generated_at: new Date().toISOString(),
          generated_by: req.user.username
        });
      });
    });
  } catch (error) {
    logger.error('Error in GET /api/reports/sf4:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// =============================================================================
// ANALYTICS ROUTES
// =============================================================================

// GET /api/analytics/dashboard - Get attendance analytics
app.get('/api/analytics/dashboard', authenticateToken, (req, res) => {
  try {
    const { period = '30' } = req.query; // Default to last 30 days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));
    const startDateStr = startDate.toISOString().split('T')[0];
    const todayStr = new Date().toISOString().split('T')[0];

    // Get overall statistics
    const queries = [
      // Total students
      "SELECT COUNT(*) as total_students FROM students WHERE status = 'active'",

      // Total classes
      "SELECT COUNT(*) as total_classes FROM classes WHERE status = 'active'",

      // Today's attendance
      `SELECT COUNT(*) as todays_attendance FROM attendance WHERE date = '${todayStr}'`,

      // Attendance by status for the period
      `SELECT status, COUNT(*) as count
       FROM attendance
       WHERE date BETWEEN '${startDateStr}' AND '${todayStr}'
       GROUP BY status`,

      // Daily attendance trend
      `SELECT date,
              COUNT(CASE WHEN status = 'present' THEN 1 END) as present,
              COUNT(CASE WHEN status = 'late' THEN 1 END) as late,
              COUNT(CASE WHEN status = 'absent' THEN 1 END) as absent,
              COUNT(*) as total
       FROM attendance
       WHERE date BETWEEN '${startDateStr}' AND '${todayStr}'
       GROUP BY date
       ORDER BY date`,

      // Grade level distribution
      `SELECT grade_level, COUNT(*) as count
       FROM students
       WHERE status = 'active'
       GROUP BY grade_level`,

      // Section distribution
      `SELECT section, grade_level, COUNT(*) as count
       FROM students
       WHERE status = 'active'
       GROUP BY grade_level, section
       ORDER BY grade_level, section`
    ];

    const results = {};
    let completedQueries = 0;

    queries.forEach((query, index) => {
      db.all(query, (err, rows) => {
        if (err) {
          logger.error(`Error in analytics query ${index}:`, err);
          return;
        }

        switch (index) {
          case 0:
            results.total_students = rows[0]?.total_students || 0;
            break;
          case 1:
            results.total_classes = rows[0]?.total_classes || 0;
            break;
          case 2:
            results.todays_attendance = rows[0]?.todays_attendance || 0;
            break;
          case 3:
            results.attendance_by_status = rows;
            break;
          case 4:
            results.daily_trend = rows;
            break;
          case 5:
            results.grade_distribution = rows;
            break;
          case 6:
            results.section_distribution = rows;
            break;
        }

        completedQueries++;
        if (completedQueries === queries.length) {
          // Calculate attendance rate
          const totalAttendance = results.attendance_by_status.reduce((sum, item) => sum + item.count, 0);
          const presentAndLate = results.attendance_by_status
            .filter(item => ['present', 'late'].includes(item.status))
            .reduce((sum, item) => sum + item.count, 0);

          results.overall_attendance_rate = totalAttendance > 0 ?
            ((presentAndLate / totalAttendance) * 100).toFixed(2) : 0;

          res.json({
            period: {
              days: parseInt(period),
              start_date: startDateStr,
              end_date: todayStr
            },
            analytics: results,
            generated_at: new Date().toISOString()
          });
        }
      });
    });
  } catch (error) {
    logger.error('Error in GET /api/analytics/dashboard:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// =============================================================================
// UTILITY ROUTES
// =============================================================================

// GET /api/classes - Get all classes
app.get('/api/classes', authenticateToken, (req, res) => {
  try {
    const { teacher_id, grade_level, section } = req.query;

    let whereClause = "WHERE c.status = 'active'";
    let params = [];

    if (teacher_id) {
      whereClause += " AND c.teacher_id = ?";
      params.push(teacher_id);
    }

    if (grade_level) {
      whereClause += " AND c.grade_level = ?";
      params.push(grade_level);
    }

    if (section) {
      whereClause += " AND c.section = ?";
      params.push(section);
    }

    const query = `
      SELECT c.*, t.first_name as teacher_first_name, t.last_name as teacher_last_name
      FROM classes c
      JOIN teachers t ON c.teacher_id = t.id
      ${whereClause}
      ORDER BY c.grade_level, c.section, c.subject_name
    `;

    db.all(query, params, (err, classes) => {
      if (err) {
        logger.error('Error fetching classes:', err);
        return res.status(500).json({ error: 'Internal server error' });
      }

      res.json({
        classes: classes.map(cls => ({
          ...cls,
          schedule: JSON.parse(cls.schedule || '{}'),
          teacher_name: `${cls.teacher_first_name} ${cls.teacher_last_name}`
        }))
      });
    });
  } catch (error) {
    logger.error('Error in GET /api/classes:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/students/qr/:qrCode - Get student by QR code
app.get('/api/students/qr/:qrCode', authenticateToken, (req, res) => {
  try {
    const { qrCode } = req.params;

    db.get("SELECT * FROM students WHERE qr_code = ? AND status = 'active'", [qrCode], (err, student) => {
      if (err) {
        logger.error('Error fetching student by QR code:', err);
        return res.status(500).json({ error: 'Internal server error' });
      }

      if (!student) {
        return res.status(404).json({ error: 'Student not found' });
      }

      res.json({ student });
    });
  } catch (error) {
    logger.error('Error in GET /api/students/qr/:qrCode:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/health - Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    database: 'connected'
  });
});

// =============================================================================
// ERROR HANDLING MIDDLEWARE
// =============================================================================

// Handle 404 errors
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    path: req.originalUrl,
    method: req.method
  });
});

// Global error handler
app.use((err, req, res, next) => {
  logger.error('Unhandled error:', err);

  // Handle multer errors
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'File too large. Maximum size is 5MB.' });
    }
    return res.status(400).json({ error: 'File upload error: ' + err.message });
  }

  // Handle other errors
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// =============================================================================
// DATABASE BACKUP AND CLEANUP
// =============================================================================

// Backup database daily
function scheduleBackup() {
  const backupInterval = 24 * 60 * 60 * 1000; // 24 hours

  setInterval(() => {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `./database/backup-${timestamp}.db`;

    // Create backup directory if it doesn't exist
    if (!fs.existsSync('./database/backups')) {
      fs.mkdirSync('./database/backups');
    }

    // Copy database file
    fs.copyFile(DB_PATH, `./database/backups/backup-${timestamp}.db`, (err) => {
      if (err) {
        logger.error('Error creating database backup:', err);
      } else {
        logger.info(`Database backup created: backup-${timestamp}.db`);
      }
    });
  }, backupInterval);
}

// Start backup scheduler
scheduleBackup();

// =============================================================================
// SERVER STARTUP
// =============================================================================

// Graceful shutdown
process.on('SIGINT', () => {
  logger.info('Received SIGINT. Graceful shutdown...');

  db.close((err) => {
    if (err) {
      logger.error('Error closing database:', err);
    } else {
      logger.info('Database connection closed');
    }
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  logger.info('Received SIGTERM. Graceful shutdown...');

  db.close((err) => {
    if (err) {
      logger.error('Error closing database:', err);
    } else {
      logger.info('Database connection closed');
    }
    process.exit(0);
  });
});

// Start server
app.listen(PORT, () => {
  logger.info(`QR-SAMS Backend Server running on port ${PORT}`);
  logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
  logger.info(`Database: ${DB_PATH}`);
  console.log(`🚀 QR-SAMS Backend Server is running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`📚 API Documentation available at: http://localhost:${PORT}/api/docs`);
});
