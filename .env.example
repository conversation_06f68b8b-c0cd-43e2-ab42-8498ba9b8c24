# QR-SAMS Backend Environment Configuration
# Copy this file to .env and update the values

# Server Configuration
PORT=3001
NODE_ENV=development

# Frontend URL for CORS
FRONTEND_URL=http://localhost:3000

# Database Configuration
DB_PATH=./database/qrsams.db

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production-minimum-32-characters

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# SMS Configuration (for future integration)
SMS_API_KEY=your-sms-api-key
SMS_API_URL=https://api.semaphore.co/api/v4/messages
SMS_SENDER_NAME=TSAT-QRSAMS

# Email Configuration (for future integration)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Backup Configuration
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30

# Security Configuration
BCRYPT_ROUNDS=10
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs

# Academic Year Configuration
CURRENT_ACADEMIC_YEAR=2024-2025
SCHOOL_NAME=Tanauan School of Arts and Trades
SCHOOL_ADDRESS=Tanauan City, Batangas, Philippines
