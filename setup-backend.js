/**
 * Backend Setup Script for QR-SAMS
 * This script helps set up the backend server dependencies and configuration
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Setting up QR-SAMS Backend Server...\n');

// Step 1: Create necessary directories
console.log('📁 Creating directories...');
const directories = [
  'database',
  'database/backups',
  'uploads',
  'uploads/photos',
  'uploads/qr-codes',
  'logs'
];

directories.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`   ✅ Created: ${dir}`);
  } else {
    console.log(`   ⏭️  Exists: ${dir}`);
  }
});

// Step 2: Copy environment file
console.log('\n🔧 Setting up environment configuration...');
if (!fs.existsSync('.env')) {
  if (fs.existsSync('.env.example')) {
    fs.copyFileSync('.env.example', '.env');
    console.log('   ✅ Created .env from .env.example');
    console.log('   ⚠️  Please edit .env with your configuration');
  } else {
    console.log('   ❌ .env.example not found');
  }
} else {
  console.log('   ⏭️  .env already exists');
}

// Step 3: Install dependencies
console.log('\n📦 Installing backend dependencies...');
try {
  // Check if backend-package.json exists
  if (fs.existsSync('backend-package.json')) {
    console.log('   📋 Found backend-package.json');
    
    // Read the backend package.json
    const backendPackage = JSON.parse(fs.readFileSync('backend-package.json', 'utf8'));
    
    // Install each dependency
    console.log('   📥 Installing dependencies...');
    const dependencies = Object.keys(backendPackage.dependencies);
    const devDependencies = Object.keys(backendPackage.devDependencies || {});
    
    // Install production dependencies
    if (dependencies.length > 0) {
      const depString = dependencies.map(dep => 
        `${dep}@${backendPackage.dependencies[dep]}`
      ).join(' ');
      
      console.log(`   Installing: ${dependencies.join(', ')}`);
      execSync(`npm install ${depString}`, { stdio: 'inherit' });
    }
    
    // Install development dependencies
    if (devDependencies.length > 0) {
      const devDepString = devDependencies.map(dep => 
        `${dep}@${backendPackage.devDependencies[dep]}`
      ).join(' ');
      
      console.log(`   Installing dev dependencies: ${devDependencies.join(', ')}`);
      execSync(`npm install --save-dev ${devDepString}`, { stdio: 'inherit' });
    }
    
    console.log('   ✅ Dependencies installed successfully');
  } else {
    console.log('   ❌ backend-package.json not found');
    console.log('   💡 Please run: cp backend-package.json package-backend.json');
  }
} catch (error) {
  console.error('   ❌ Error installing dependencies:', error.message);
  console.log('   💡 You may need to install dependencies manually');
}

// Step 4: Create startup scripts
console.log('\n📝 Creating startup scripts...');

// Create start script
const startScript = `#!/bin/bash
# QR-SAMS Backend Startup Script

echo "🚀 Starting QR-SAMS Backend Server..."

# Check if .env exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please copy .env.example to .env and configure it."
    exit 1
fi

# Check if node_modules exists
if [ ! -d node_modules ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Start the server
echo "🌟 Starting server on port 3001..."
node server.js
`;

fs.writeFileSync('start-backend.sh', startScript);
fs.chmodSync('start-backend.sh', '755');
console.log('   ✅ Created start-backend.sh');

// Create Windows batch file
const startBatch = `@echo off
REM QR-SAMS Backend Startup Script for Windows

echo 🚀 Starting QR-SAMS Backend Server...

REM Check if .env exists
if not exist .env (
    echo ❌ .env file not found. Please copy .env.example to .env and configure it.
    pause
    exit /b 1
)

REM Check if node_modules exists
if not exist node_modules (
    echo 📦 Installing dependencies...
    npm install
)

REM Start the server
echo 🌟 Starting server on port 3001...
node server.js
pause
`;

fs.writeFileSync('start-backend.bat', startBatch);
console.log('   ✅ Created start-backend.bat');

// Step 5: Final instructions
console.log('\n🎉 Backend setup completed!\n');
console.log('📋 Next steps:');
console.log('   1. Edit .env file with your configuration');
console.log('   2. Run: node server.js (or ./start-backend.sh on Unix/Linux)');
console.log('   3. Server will be available at http://localhost:3001');
console.log('   4. Health check: http://localhost:3001/api/health');
console.log('   5. Default admin login: admin / admin123');
console.log('\n⚠️  Remember to change default passwords in production!');
console.log('\n📚 See BACKEND-README.md for detailed documentation');
