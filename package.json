{"name": "qrsams", "version": "1.2.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"apexcharts": "^4.5.0", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compression": "^1.8.1", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "flatpickr": "^4.6.13", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "jsvectormap": "^1.6.0", "moment": "^2.30.1", "multer": "^2.0.2", "next": "15.1.6", "next-themes": "^0.4.4", "nextjs-toploader": "^3.7.15", "node-cron": "^4.2.1", "qrcode": "^1.5.4", "react": "19.0.0", "react-apexcharts": "^1.7.0", "react-dom": "19.0.0", "sqlite3": "^5.1.7", "tailwind-merge": "^2.6.0", "winston": "^3.17.0"}, "devDependencies": {"@types/node": "^22", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.16", "typescript": "^5"}}