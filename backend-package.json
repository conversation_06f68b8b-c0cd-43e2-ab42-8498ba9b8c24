{"name": "qrsams-backend", "version": "1.0.0", "description": "QR-Code Based Student Attendance and Monitoring System - Backend Server for Tanauan School of Arts and Trades", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "backup": "node scripts/backup.js", "migrate": "node scripts/migrate.js"}, "keywords": ["attendance", "qr-code", "student-management", "school-system", "nodejs", "express", "sqlite"], "author": "Tanauan School of Arts and Trades", "license": "MIT", "dependencies": {"express": "^4.18.2", "sqlite3": "^5.1.6", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "qrcode": "^1.5.3", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "compression": "^1.7.4", "winston": "^3.11.0", "dotenv": "^16.3.1", "joi": "^17.11.0", "moment": "^2.29.4", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/node": "^20.10.5"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/tanauan-school/qrsams-backend.git"}, "bugs": {"url": "https://github.com/tanauan-school/qrsams-backend/issues"}, "homepage": "https://github.com/tanauan-school/qrsams-backend#readme"}