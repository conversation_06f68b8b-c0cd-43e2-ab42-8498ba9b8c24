# QR-SAMS Backend Server

QR-Code Based Student Attendance and Monitoring System for Tanauan School of Arts and Trades - Backend API Server

## Features

- **Express.js REST API** with comprehensive endpoints
- **SQLite3 Database** with automatic initialization
- **JWT Authentication** with bcrypt password hashing
- **QR Code Generation** using crypto-secure hashing
- **File Upload Support** for student photos
- **SMS Notification System** (ready for integration)
- **Comprehensive Reporting** (SF2, SF4 forms)
- **Real-time Analytics Dashboard**
- **Rate Limiting & Security** with Helmet.js
- **Automatic Database Backups**
- **Filipino Context** with DepEd-compliant features

## Quick Start

### 1. Install Dependencies

```bash
# Copy the backend package.json
cp backend-package.json package-backend.json

# Install backend dependencies
npm install --prefix backend
```

### 2. Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your configuration
```

### 3. Run the Server

```bash
# Development mode with auto-reload
npm run dev

# Production mode
npm start
```

The server will start on `http://localhost:3001`

## API Endpoints

### Authentication
- `POST /api/auth/login` - Teacher login
- `POST /api/auth/logout` - Teacher logout
- `GET /api/auth/verify` - Verify token

### Student Management
- `GET /api/students` - Get all students (paginated)
- `POST /api/students` - Add new student (with photo upload)
- `GET /api/students/:id` - Get student profile
- `GET /api/students/qr/:qrCode` - Get student by QR code

### Attendance Management
- `POST /api/attendance/mark` - Mark attendance via QR scan
- `GET /api/attendance/class/:classId` - Get class attendance

### Classes
- `GET /api/classes` - Get all classes

### SMS Notifications
- `POST /api/sms/send` - Send SMS notification to parents

### Reports & Analytics
- `GET /api/reports/sf2` - Generate SF2 form data
- `GET /api/reports/sf4` - Generate SF4 form data
- `GET /api/analytics/dashboard` - Get attendance analytics

### Utility
- `GET /api/health` - Health check

## Database Schema

### Students Table
- Student information with Filipino naming conventions
- QR code generation for each student
- Photo upload support
- Grade levels: Grade 11, Grade 12
- Status tracking (active, inactive, transferred, graduated)

### Teachers Table
- Teacher authentication and authorization
- Role-based access (teacher, admin, principal)
- Subject assignments

### Classes Table
- Subject and schedule management
- Room assignments
- Academic year tracking

### Attendance Table
- Daily attendance tracking
- Multiple status types (present, late, absent, excused)
- Teacher attribution

### Parents Table
- Parent/guardian contact information
- Philippine mobile number validation
- Primary contact designation

### SMS Logs Table
- SMS notification tracking
- Delivery status monitoring

## Default Credentials

**Admin Account:**
- Username: `admin`
- Password: `admin123`

**Teacher Accounts:**
- Username: `jdelacruz` / Password: `teacher123`
- Username: `areyes` / Password: `teacher123`

⚠️ **Change these credentials in production!**

## Security Features

- JWT token authentication
- bcrypt password hashing
- Rate limiting (100 requests per 15 minutes)
- Helmet.js security headers
- Input validation and sanitization
- File upload restrictions
- CORS configuration

## File Structure

```
├── server.js              # Main server file
├── database/              # SQLite database files
│   ├── qrsams.db         # Main database
│   └── backups/          # Automatic backups
├── uploads/              # File uploads
│   ├── photos/           # Student photos
│   └── qr-codes/         # Generated QR codes
├── logs/                 # Application logs
│   ├── error.log         # Error logs
│   └── combined.log      # All logs
└── .env                  # Environment configuration
```

## Environment Variables

See `.env.example` for all available configuration options.

## Development

### Running Tests
```bash
npm test
```

### Database Backup
```bash
npm run backup
```

### Logs
Application logs are stored in the `logs/` directory:
- `error.log` - Error-level logs only
- `combined.log` - All application logs

## Production Deployment

1. Set `NODE_ENV=production` in your environment
2. Use a strong JWT secret (minimum 32 characters)
3. Configure proper CORS origins
4. Set up SSL/TLS termination
5. Configure log rotation
6. Set up monitoring and alerting

## Filipino Context Features

- **Grade Levels**: Grade 11, Grade 12 (Senior High School)
- **Student Numbering**: DepEd-compliant format (YYYY-NNNN)
- **Phone Validation**: Philippine mobile number format (+63XXXXXXXXXX)
- **Sample Data**: Filipino names and common sections (STEM, ABM, HUMSS)
- **Academic Year**: Philippine school year format (2024-2025)

## Support

For technical support or questions about the QR-SAMS system, please contact the IT department at Tanauan School of Arts and Trades.
